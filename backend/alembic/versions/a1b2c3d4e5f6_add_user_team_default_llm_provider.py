"""Add user team default LLM provider table

Revision ID: a1b2c3d4e5f6
Revises: fec3db967bf7
Create Date: 2025-01-25 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = "a1b2c3d4e5f6"
down_revision = "20240322"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Create the user_team_default_llm_provider table
    op.create_table(
        "user_team_default_llm_provider",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("user_group_id", sa.Integer(), nullable=False),
        sa.Column("llm_provider_id", sa.Integer(), nullable=False),
        sa.Column(
            "created_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["user_group_id"],
            ["user_group.id"],
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["llm_provider_id"],
            ["llm_provider.id"],
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("user_group_id"),  # Each user group can have only one default LLM provider
    )

    # Create index for faster lookups
    op.create_index(
        "ix_user_team_default_llm_provider_user_group_id",
        "user_team_default_llm_provider",
        ["user_group_id"],
    )
    op.create_index(
        "ix_user_team_default_llm_provider_llm_provider_id",
        "user_team_default_llm_provider",
        ["llm_provider_id"],
    )

    # Populate the table with default LLM providers for existing teams
    # This will set the first available LLM provider from llm_provider__user_group as default
    op.execute(
        """
        INSERT INTO user_team_default_llm_provider (user_group_id, llm_provider_id)
        SELECT DISTINCT ON (lpug.user_group_id) 
               lpug.user_group_id, 
               lpug.llm_provider_id
        FROM llm_provider__user_group lpug
        INNER JOIN llm_provider lp ON lpug.llm_provider_id = lp.id
        ORDER BY lpug.user_group_id, lpug.llm_provider_id
        """
    )


def downgrade() -> None:
    # Drop indexes first
    op.drop_index("ix_user_team_default_llm_provider_llm_provider_id")
    op.drop_index("ix_user_team_default_llm_provider_user_group_id")
    
    # Drop the table
    op.drop_table("user_team_default_llm_provider")
