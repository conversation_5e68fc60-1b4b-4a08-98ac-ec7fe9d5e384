from sqlalchemy import delete
from sqlalchemy import or_
from sqlalchemy import select
from sqlalchemy.sql import func
from sqlalchemy.orm import Session

from onyx.configs.app_configs import AUTH_TYPE
from onyx.configs.constants import AuthType
from onyx.db.models import CloudEmbeddingProvider as CloudEmbeddingProviderModel
from onyx.db.models import DocumentSet
from onyx.db.models import LLMProvider as LLMProviderModel
from onyx.db.models import LLMProvider__UserGroup
from onyx.db.models import SearchSettings
from onyx.db.models import Tool as ToolModel
from onyx.db.models import User
from onyx.db.models import User__UserGroup
from onyx.db.models import UserGroup
from onyx.db.models import UserTeamDefaultLLMProvider
from onyx.server.manage.embedding.models import CloudEmbeddingProvider
from onyx.server.manage.embedding.models import CloudEmbeddingProviderCreationRequest
from onyx.server.manage.llm.models import FullLLMProvider
from onyx.server.manage.llm.models import LLMProviderUpsertRequest
from shared_configs.enums import EmbeddingProvider
from onyx.auth.schemas import User<PERSON><PERSON>


def update_group_llm_provider_relationships__no_commit(
    llm_provider_id: int,
    user_team_ids: list[int] | None,
    db_session: Session,
) -> None:
    # Delete existing relationships
    db_session.query(LLMProvider__UserGroup).filter(
        LLMProvider__UserGroup.llm_provider_id == llm_provider_id
    ).delete(synchronize_session="fetch")

    # Add new relationships from given user_team_ids
    if user_team_ids:
        new_relationships = [
            LLMProvider__UserGroup(
                llm_provider_id=llm_provider_id,
                user_group_id=user_team_id,
            )
            for user_team_id in user_team_ids
        ]
        db_session.add_all(new_relationships)


def upsert_cloud_embedding_provider(
    db_session: Session, provider: CloudEmbeddingProviderCreationRequest
) -> CloudEmbeddingProvider:
    existing_provider = (
        db_session.query(CloudEmbeddingProviderModel)
        .filter_by(provider_type=provider.provider_type)
        .first()
    )
    if existing_provider:
        for key, value in provider.model_dump().items():
            setattr(existing_provider, key, value)
    else:
        new_provider = CloudEmbeddingProviderModel(**provider.model_dump())

        db_session.add(new_provider)
        existing_provider = new_provider
    db_session.commit()
    db_session.refresh(existing_provider)
    return CloudEmbeddingProvider.from_request(existing_provider)


def upsert_llm_provider(
    llm_provider: LLMProviderUpsertRequest,
    db_session: Session,
) -> FullLLMProvider:
    existing_llm_provider = db_session.scalar(
        select(LLMProviderModel).where(LLMProviderModel.name == llm_provider.name)
    )

    if not existing_llm_provider:
        existing_llm_provider = LLMProviderModel(name=llm_provider.name)
        db_session.add(existing_llm_provider)
    
    if llm_provider.user_teams:
        with db_session.no_autoflush:
            user_teams = db_session.query(UserGroup).filter(UserGroup.id.in_(llm_provider.user_teams)).all()
    else:
        user_teams = []

    existing_llm_provider.provider = llm_provider.provider
    existing_llm_provider.api_key = llm_provider.api_key
    existing_llm_provider.api_base = llm_provider.api_base
    existing_llm_provider.api_version = llm_provider.api_version
    existing_llm_provider.custom_config = llm_provider.custom_config
    existing_llm_provider.default_model_name = llm_provider.default_model_name
    existing_llm_provider.fast_default_model_name = llm_provider.fast_default_model_name
    existing_llm_provider.model_names = llm_provider.model_names
    existing_llm_provider.is_public = llm_provider.is_public
    existing_llm_provider.groups = user_teams
    existing_llm_provider.display_model_names = llm_provider.display_model_names
    existing_llm_provider.deployment_name = llm_provider.deployment_name

    if not existing_llm_provider.id:
        # If its not already in the db, we need to generate an ID by flushing
        db_session.flush()

    # Make sure the relationship table stays up to date
    update_group_llm_provider_relationships__no_commit(
        llm_provider_id=existing_llm_provider.id,
        user_team_ids=llm_provider.user_teams,
        db_session=db_session,
    )
    full_llm_provider = FullLLMProvider.from_model(existing_llm_provider)

    db_session.commit()

    return full_llm_provider


def fetch_existing_embedding_providers(
    db_session: Session,
) -> list[CloudEmbeddingProviderModel]:
    return list(db_session.scalars(select(CloudEmbeddingProviderModel)).all())


def fetch_existing_doc_sets(
    db_session: Session, doc_ids: list[int]
) -> list[DocumentSet]:
    return list(
        db_session.scalars(select(DocumentSet).where(DocumentSet.id.in_(doc_ids))).all()
    )


def fetch_existing_tools(db_session: Session, tool_ids: list[int]) -> list[ToolModel]:
    return list(
        db_session.scalars(select(ToolModel).where(ToolModel.id.in_(tool_ids))).all()
    )


def fetch_existing_llm_providers(
    db_session: Session,
) -> list[LLMProviderModel]:
    stmt = select(LLMProviderModel)
    return list(db_session.scalars(stmt).all())


def fetch_existing_llm_providers_for_user(
    db_session: Session,
    user: User | None = None,
) -> list[LLMProviderModel]:
    """
    Returns LLM providers available to the user:
    - Admin: all providers (public and private)
    - Team Admin/Basic: public providers and private providers where user is in the user team
    """
    if not user:
        if AUTH_TYPE != AuthType.DISABLED:
            # User is anonymous
            return list(
                db_session.scalars(
                    select(LLMProviderModel).where(
                        LLMProviderModel.is_public == True  # noqa: E712
                    )
                ).all()
            )
        else:
            # If auth is disabled, user has access to all providers
            return fetch_existing_llm_providers(db_session)

    # Admin: all providers
    if user.role == UserRole.ADMIN:
        return fetch_existing_llm_providers(db_session)

    # Team Admin/Basic: public + private where user is in the user team
    stmt = select(LLMProviderModel).distinct()
    user_groups_select = select(User__UserGroup.user_group_id).where(
        User__UserGroup.user_id == user.id
    )
    access_conditions = or_(
        LLMProviderModel.is_public,
        LLMProviderModel.id.in_(  # User is part of a group that has access
            select(LLMProvider__UserGroup.llm_provider_id).where(
                LLMProvider__UserGroup.user_group_id.in_(user_groups_select)  # type: ignore
            )
        ),
    )
    stmt = stmt.where(access_conditions)
    return list(db_session.scalars(stmt).all())


def fetch_embedding_provider(
    db_session: Session, provider_type: EmbeddingProvider
) -> CloudEmbeddingProviderModel | None:
    return db_session.scalar(
        select(CloudEmbeddingProviderModel).where(
            CloudEmbeddingProviderModel.provider_type == provider_type
        )
    )


def fetch_default_provider(db_session: Session) -> FullLLMProvider | None:
    provider_model = db_session.scalar(
        select(LLMProviderModel).where(
            LLMProviderModel.is_default_provider == True  # noqa: E712
        )
    )
    if not provider_model:
        return None
    return FullLLMProvider.from_model(provider_model)


def fetch_provider(db_session: Session, provider_name: str) -> FullLLMProvider | None:
    provider_model = db_session.scalar(
        select(LLMProviderModel).where(LLMProviderModel.name == provider_name)
    )
    if not provider_model:
        return None
    return FullLLMProvider.from_model(provider_model)


def fetch_provider_by_id(db_session: Session, provider_id: int) -> FullLLMProvider | None:
    provider_model = db_session.scalar(
        select(LLMProviderModel).where(LLMProviderModel.id == provider_id)
    )
    if not provider_model:
        return None
    return FullLLMProvider.from_model(provider_model)


def can_user_edit_llm_provider(user: User, provider: FullLLMProvider) -> bool:
    """
    Check if a user can edit/delete an LLM provider.
    - Admin users can edit any provider
    - Team admin users can only edit providers assigned to their teams (not public ones)
    """
    if user.role == UserRole.ADMIN:
        return True

    if user.role in (UserRole.TEAM_ADMIN, UserRole.BASIC):
        # Team admin can only edit private providers assigned to their teams
        if provider.is_public:
            return False  # Cannot edit public providers (created by admin)

        # Check if any of the user's teams are assigned to this provider
        user_team_ids = set(user.user_team_ids)
        provider_team_ids = set(provider.user_teams)
        return bool(user_team_ids.intersection(provider_team_ids))

    return False


def remove_embedding_provider(
    db_session: Session, provider_type: EmbeddingProvider
) -> None:
    db_session.execute(
        delete(SearchSettings).where(SearchSettings.provider_type == provider_type)
    )

    # Delete the embedding provider
    db_session.execute(
        delete(CloudEmbeddingProviderModel).where(
            CloudEmbeddingProviderModel.provider_type == provider_type
        )
    )

    db_session.commit()


def remove_llm_provider(db_session: Session, provider_id: int) -> None:
    # Get provider info before deletion for cleanup
    provider_to_delete = db_session.scalar(
        select(LLMProviderModel).where(LLMProviderModel.id == provider_id)
    )

    if provider_to_delete:
        # Clean up available_llm_models in all personas that reference this provider
        _cleanup_personas_available_models(db_session, provider_to_delete.name, provider_to_delete.provider)

        # If this was the default provider, we need to handle personas that might have
        # been auto-populated with this provider's default model
        if provider_to_delete.is_default_provider:
            _handle_default_provider_deletion(db_session)

    # Remove LLMProvider's dependent relationships
    db_session.execute(
        delete(LLMProvider__UserGroup).where(
            LLMProvider__UserGroup.llm_provider_id == provider_id
        )
    )
    # Remove LLMProvider
    db_session.execute(
        delete(LLMProviderModel).where(LLMProviderModel.id == provider_id)
    )
    db_session.commit()


def _cleanup_personas_available_models(
    db_session: Session, provider_name: str, provider_type: str
) -> None:
    """Remove models from the deleted provider from all personas' available_llm_models lists."""
    from onyx.db.models import Persona

    # Get all personas that have available_llm_models configured
    personas_with_models = db_session.scalars(
        select(Persona).where(Persona.available_llm_models.isnot(None))
    ).all()

    for persona in personas_with_models:
        if persona.available_llm_models:
            # Filter out models from the deleted provider
            # Format: "provider_name__provider_type__model_name"
            updated_models = [
                model for model in persona.available_llm_models
                if not model.startswith(f"{provider_name}__{provider_type}__")
            ]

            # Update the persona's available_llm_models
            persona.available_llm_models = updated_models if updated_models else []

    # Commit the changes
    db_session.commit()


def _handle_default_provider_deletion(db_session: Session) -> None:
    """Handle special case when the default provider is deleted.

    This function addresses personas that might have only had the default provider's
    model in their available_llm_models list (due to auto-population when the list was empty).
    After removing the default provider's models, if a persona's list becomes empty,
    we leave it empty rather than auto-populating with a new default.
    """
    from onyx.db.models import Persona

    # Get all personas that now have empty available_llm_models after cleanup
    personas_with_empty_models = db_session.scalars(
        select(Persona).where(
            (Persona.available_llm_models == []) |
            (Persona.available_llm_models.is_(None))
        )
    ).all()

    # For these personas, we explicitly set available_llm_models to empty list
    # This ensures they don't get auto-populated with a new default provider
    # until the user explicitly configures them
    for persona in personas_with_empty_models:
        persona.available_llm_models = []

    # Commit the changes
    db_session.commit()


def update_default_provider(provider_id: int, db_session: Session) -> None:
    new_default = db_session.scalar(
        select(LLMProviderModel).where(LLMProviderModel.id == provider_id)
    )
    if not new_default:
        raise ValueError(f"LLM Provider with id {provider_id} does not exist")

    existing_default = db_session.scalar(
        select(LLMProviderModel).where(
            LLMProviderModel.is_default_provider == True  # noqa: E712
        )
    )
    if existing_default:
        existing_default.is_default_provider = None
        # required to ensure that the below does not cause a unique constraint violation
        db_session.flush()

    new_default.is_default_provider = True
    db_session.commit()


def get_user_team_default_llm_provider(
    user_group_id: int, db_session: Session
) -> LLMProviderModel | None:
    """Get the default LLM provider for a specific user team."""
    default_provider_relation = db_session.scalar(
        select(UserTeamDefaultLLMProvider).where(
            UserTeamDefaultLLMProvider.user_group_id == user_group_id
        )
    )
    if default_provider_relation:
        return db_session.scalar(
            select(LLMProviderModel).where(
                LLMProviderModel.id == default_provider_relation.llm_provider_id
            )
        )
    return None


def set_user_team_default_llm_provider(
    user_group_id: int, llm_provider_id: int, db_session: Session
) -> None:
    """Set the default LLM provider for a specific user team."""
    # Verify that the LLM provider exists
    llm_provider = db_session.scalar(
        select(LLMProviderModel).where(LLMProviderModel.id == llm_provider_id)
    )
    if not llm_provider:
        raise ValueError(f"LLM Provider with id {llm_provider_id} does not exist")

    # Verify that the user group exists
    user_group = db_session.scalar(
        select(UserGroup).where(UserGroup.id == user_group_id)
    )
    if not user_group:
        raise ValueError(f"User group with id {user_group_id} does not exist")

    # Verify that the LLM provider is available to this user group
    provider_access = db_session.scalar(
        select(LLMProvider__UserGroup).where(
            LLMProvider__UserGroup.user_group_id == user_group_id,
            LLMProvider__UserGroup.llm_provider_id == llm_provider_id,
        )
    )
    if not provider_access:
        raise ValueError(
            f"LLM Provider {llm_provider_id} is not available to user group {user_group_id}"
        )

    # Check if a default already exists for this user group
    existing_default = db_session.scalar(
        select(UserTeamDefaultLLMProvider).where(
            UserTeamDefaultLLMProvider.user_group_id == user_group_id
        )
    )

    if existing_default:
        # Update existing default
        existing_default.llm_provider_id = llm_provider_id
        existing_default.updated_at = func.now()
    else:
        # Create new default
        new_default = UserTeamDefaultLLMProvider(
            user_group_id=user_group_id,
            llm_provider_id=llm_provider_id,
        )
        db_session.add(new_default)

    db_session.commit()


def get_or_set_fallback_user_team_default_llm_provider(
    user_group_id: int, db_session: Session
) -> LLMProviderModel | None:
    """
    Get the default LLM provider for a user team. If no default is set,
    automatically set the first available LLM provider as default.
    """
    # First try to get existing default
    default_provider = get_user_team_default_llm_provider(user_group_id, db_session)
    if default_provider:
        return default_provider

    # If no default exists, find the first available LLM provider for this team
    available_provider = db_session.scalar(
        select(LLMProviderModel)
        .join(LLMProvider__UserGroup)
        .where(LLMProvider__UserGroup.user_group_id == user_group_id)
        .order_by(LLMProviderModel.id)
    )

    if available_provider:
        # Set it as default
        set_user_team_default_llm_provider(
            user_group_id, available_provider.id, db_session
        )
        return available_provider

    return None


def remove_user_team_default_llm_provider(
    user_group_id: int, db_session: Session
) -> None:
    """Remove the default LLM provider setting for a user team."""
    db_session.execute(
        delete(UserTeamDefaultLLMProvider).where(
            UserTeamDefaultLLMProvider.user_group_id == user_group_id
        )
    )
    db_session.commit()
