import uuid

import requests

from tests.integration.common_utils.constants import API_SERVER_URL
from tests.integration.common_utils.managers.user import UserManager
from tests.integration.common_utils.managers.user_group import UserGroupManager
from tests.integration.common_utils.test_models import DATestUser
from onyx.auth.schemas import UserRole


_DEFAULT_MODELS = ["gpt-4", "gpt-4o"]


def _get_provider_by_id(admin_user: DATestUser, provider_id: str) -> dict | None:
    """Utility function to fetch an LLM provider by ID"""
    response = requests.get(
        f"{API_SERVER_URL}/admin/llm/provider",
        headers=admin_user.headers,
    )
    assert response.status_code == 200
    providers = response.json()
    return next((p for p in providers if p["id"] == provider_id), None)


def test_create_llm_provider_without_display_model_names(reset: None) -> None:
    """Test creating an LLM provider without specifying
    display_model_names and verify it's null in response"""
    # Create admin user
    admin_user = UserManager.create(name="admin_user")

    # Create LLM provider without model_names
    response = requests.put(
        f"{API_SERVER_URL}/admin/llm/provider?is_creation=true",
        headers=admin_user.headers,
        json={
            "name": str(uuid.uuid4()),
            "provider": "openai",
            "default_model_name": _DEFAULT_MODELS[0],
            "model_names": _DEFAULT_MODELS,
            "is_public": True,
            "groups": [],
        },
    )
    assert response.status_code == 200
    created_provider = response.json()
    provider_data = _get_provider_by_id(admin_user, created_provider["id"])

    # Verify model_names is None/null
    assert provider_data is not None
    assert provider_data["model_names"] == _DEFAULT_MODELS
    assert provider_data["default_model_name"] == _DEFAULT_MODELS[0]
    assert provider_data["display_model_names"] is None


def test_update_llm_provider_model_names(reset: None) -> None:
    """Test updating an LLM provider's model_names"""
    # Create admin user
    admin_user = UserManager.create(name="admin_user")

    # First create provider without model_names
    name = str(uuid.uuid4())
    response = requests.put(
        f"{API_SERVER_URL}/admin/llm/provider?is_creation=true",
        headers=admin_user.headers,
        json={
            "name": name,
            "provider": "openai",
            "default_model_name": _DEFAULT_MODELS[0],
            "model_names": [_DEFAULT_MODELS[0]],
            "is_public": True,
            "groups": [],
        },
    )
    assert response.status_code == 200
    created_provider = response.json()

    # Update with model_names
    response = requests.put(
        f"{API_SERVER_URL}/admin/llm/provider",
        headers=admin_user.headers,
        json={
            "id": created_provider["id"],
            "name": name,
            "provider": created_provider["provider"],
            "default_model_name": _DEFAULT_MODELS[0],
            "model_names": _DEFAULT_MODELS,
            "is_public": True,
            "groups": [],
        },
    )
    assert response.status_code == 200

    # Verify update
    provider_data = _get_provider_by_id(admin_user, created_provider["id"])
    assert provider_data is not None
    assert provider_data["model_names"] == _DEFAULT_MODELS


def test_delete_llm_provider(reset: None) -> None:
    """Test deleting an LLM provider"""
    # Create admin user
    admin_user = UserManager.create(name="admin_user")

    # Create a provider
    response = requests.put(
        f"{API_SERVER_URL}/admin/llm/provider?is_creation=true",
        headers=admin_user.headers,
        json={
            "name": "test-provider-delete",
            "provider": "openai",
            "default_model_name": _DEFAULT_MODELS[0],
            "model_names": _DEFAULT_MODELS,
            "is_public": True,
            "groups": [],
        },
    )
    assert response.status_code == 200
    created_provider = response.json()

    # Delete the provider
    response = requests.delete(
        f"{API_SERVER_URL}/admin/llm/provider/{created_provider['id']}",
        headers=admin_user.headers,
    )
    assert response.status_code == 200

    # Verify provider is deleted by checking it's not in the list
    provider_data = _get_provider_by_id(admin_user, created_provider["id"])
    assert provider_data is None


def test_team_admin_llm_provider_restrictions(reset: None) -> None:
    """Test that team_admin users can only create private LLM providers assigned to their teams"""
    # Create admin user
    admin_user = UserManager.create(name="admin_user")

    # Create team_admin user
    team_admin_user = UserManager.create(name="team_admin_user", role=UserRole.TEAM_ADMIN)

    # Create a team and assign team_admin to it
    user_group = UserGroupManager.create(
        name="test_team",
        user_ids=[team_admin_user.id],
        cc_pair_ids=[],
        user_performing_action=admin_user,
    )

    # Test 1: team_admin tries to create a public LLM provider (should fail)
    response = requests.put(
        f"{API_SERVER_URL}/admin/llm/provider?is_creation=true",
        headers=team_admin_user.headers,
        json={
            "name": "team-admin-public-provider",
            "provider": "openai",
            "default_model_name": _DEFAULT_MODELS[0],
            "model_names": _DEFAULT_MODELS,
            "is_public": True,  # This should be rejected
            "user_teams": [],
        },
    )
    assert response.status_code == 400
    assert "cannot set LLM provider as public" in response.json()["detail"]

    # Test 2: team_admin tries to manually assign teams (should fail)
    response = requests.put(
        f"{API_SERVER_URL}/admin/llm/provider?is_creation=true",
        headers=team_admin_user.headers,
        json={
            "name": "team-admin-manual-teams",
            "provider": "openai",
            "default_model_name": _DEFAULT_MODELS[0],
            "model_names": _DEFAULT_MODELS,
            "is_public": False,
            "user_teams": [user_group.id],  # This should be rejected
        },
    )
    assert response.status_code == 400
    assert "not allowed to assign a team" in response.json()["detail"]

    # Test 3: team_admin creates a valid private provider (should succeed)
    response = requests.put(
        f"{API_SERVER_URL}/admin/llm/provider?is_creation=true",
        headers=team_admin_user.headers,
        json={
            "name": "team-admin-valid-provider",
            "provider": "openai",
            "default_model_name": _DEFAULT_MODELS[0],
            "model_names": _DEFAULT_MODELS,
            "is_public": False,
            "user_teams": [],  # Backend should auto-assign team
        },
    )
    assert response.status_code == 200
    created_provider = response.json()

    # Verify the provider was created with correct settings
    provider_data = _get_provider_by_id(admin_user, created_provider["id"])
    assert provider_data is not None
    assert provider_data["is_public"] is False
    assert user_group.id in provider_data["user_teams"]

    # Test 4: team_admin can only see their team's providers + public ones
    # First, create a public provider as admin
    admin_response = requests.put(
        f"{API_SERVER_URL}/admin/llm/provider?is_creation=true",
        headers=admin_user.headers,
        json={
            "name": "admin-public-provider",
            "provider": "openai",
            "default_model_name": _DEFAULT_MODELS[0],
            "model_names": _DEFAULT_MODELS,
            "is_public": True,
            "user_teams": [],
        },
    )
    assert admin_response.status_code == 200

    # Create another private provider for a different team (team_admin shouldn't see this)
    other_team_admin = UserManager.create(name="other_team_admin", role=UserRole.TEAM_ADMIN)
    other_user_group = UserGroupManager.create(
        name="other_test_team",
        user_ids=[other_team_admin.id],
        cc_pair_ids=[],
        user_performing_action=admin_user,
    )

    other_response = requests.put(
        f"{API_SERVER_URL}/admin/llm/provider?is_creation=true",
        headers=other_team_admin.headers,
        json={
            "name": "other-team-provider",
            "provider": "openai",
            "default_model_name": _DEFAULT_MODELS[0],
            "model_names": _DEFAULT_MODELS,
            "is_public": False,
            "user_teams": [],
        },
    )
    assert other_response.status_code == 200

    # Check what team_admin can see
    team_admin_providers_response = requests.get(
        f"{API_SERVER_URL}/admin/llm/provider",
        headers=team_admin_user.headers,
    )
    assert team_admin_providers_response.status_code == 200
    team_admin_providers = team_admin_providers_response.json()

    # team_admin should see:
    # 1. Their own private provider
    # 2. The admin's public provider
    # But NOT the other team's private provider
    provider_names = [p["name"] for p in team_admin_providers]
    assert "team-admin-valid-provider" in provider_names
    assert "admin-public-provider" in provider_names
    assert "other-team-provider" not in provider_names
